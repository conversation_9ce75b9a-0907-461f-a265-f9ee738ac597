#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器批量登录测试脚本
用于测试多个用户账户的登录情况
"""

import requests
import json
import re
import random
import time
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import hashlib
import csv
from datetime import datetime

def encrypt_password(password, key="skyeye"):
    """
    加密密码 (模拟前端加密逻辑)
    """
    try:
        key_bytes = key.encode('utf-8')
        key_hash = hashlib.md5(key_bytes).digest()
        salt = b'Salted__' + bytes([random.randint(0, 255) for _ in range(8)])
        derived_key = hashlib.md5(key_hash + salt[8:]).digest()
        cipher = AES.new(derived_key, AES.MODE_CBC, derived_key[:16])
        padded_password = pad(password.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_password)
        encrypted_data = salt + encrypted
        return base64.b64encode(encrypted_data).decode('utf-8')
    except Exception as e:
        print(f"密码加密失败: {e}")
        return "U2FsdGVkX18woJynzevdp9AF+c6KpWiR0H+dfkYJfDo="

def test_login(base_url, username, password, timeout=10):
    """
    测试单个账户登录
    
    Args:
        base_url (str): 服务器地址
        username (str): 用户名
        password (str): 密码
        timeout (int): 超时时间
    
    Returns:
        dict: 测试结果
    """
    result = {
        'username': username,
        'password': password,
        'success': False,
        'message': '',
        'response_time': 0,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    session = requests.Session()
    session.timeout = timeout
    
    # 设置请求头
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    })
    
    start_time = time.time()
    
    try:
        # 获取登录页面和CSRF token
        login_page_url = f"{base_url}/sensor/login"
        response = session.get(login_page_url)
        
        if response.status_code != 200:
            result['message'] = f"获取登录页面失败: {response.status_code}"
            return result
        
        # 提取CSRF token
        csrf_match = re.search(r'content="([a-f0-9]{32})"', response.text)
        if not csrf_match:
            result['message'] = "未找到CSRF token"
            return result
        
        csrf_token = csrf_match.group(1)
        
        # 尝试登录（不使用验证码，某些情况下可能不需要）
        encrypted_password = encrypt_password(password)
        
        login_url = f"{base_url}/skyeye/admin/login"
        login_data = {
            "type_login": "sys",
            "username": username,
            "password": encrypted_password,
            "authcode": "",  # 空验证码
            "csrf_token": csrf_token,
            "r": random.random()
        }
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': base_url,
            'Referer': f'{base_url}/sensor/login'
        }
        
        login_response = session.post(login_url, headers=headers, json=login_data)
        
        end_time = time.time()
        result['response_time'] = round((end_time - start_time) * 1000, 2)  # 毫秒
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            result['message'] = login_result.get('message', 'Unknown')
            
            if login_result.get('status') == 200:
                result['success'] = True
                user_data = login_result.get('data', {})
                result['message'] = f"登录成功 - 用户: {user_data.get('username', 'Unknown')}"
            else:
                result['success'] = False
        else:
            result['message'] = f"HTTP错误: {login_response.status_code}"
            
    except requests.exceptions.Timeout:
        result['message'] = "请求超时"
    except requests.exceptions.ConnectionError:
        result['message'] = "连接错误"
    except Exception as e:
        result['message'] = f"未知错误: {str(e)}"
    
    return result

def load_credentials_from_file(filename):
    """
    从文件加载用户凭据
    支持CSV和TXT格式
    """
    credentials = []
    
    try:
        if filename.endswith('.csv'):
            with open(filename, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row in reader:
                    if len(row) >= 2:
                        credentials.append((row[0].strip(), row[1].strip()))
        else:
            # TXT格式，每行格式: username:password 或 username,password
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if ':' in line:
                        username, password = line.split(':', 1)
                        credentials.append((username.strip(), password.strip()))
                    elif ',' in line:
                        username, password = line.split(',', 1)
                        credentials.append((username.strip(), password.strip()))
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
    except Exception as e:
        print(f"读取文件失败: {e}")
    
    return credentials

def save_results_to_csv(results, filename):
    """
    保存测试结果到CSV文件
    """
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['用户名', '密码', '登录结果', '响应时间(ms)', '消息', '测试时间'])
            
            for result in results:
                writer.writerow([
                    result['username'],
                    result['password'],
                    '成功' if result['success'] else '失败',
                    result['response_time'],
                    result['message'],
                    result['timestamp']
                ])
        print(f"结果已保存到 {filename}")
    except Exception as e:
        print(f"保存结果失败: {e}")

def batch_test_login(base_url, credentials, delay=1):
    """
    批量测试登录
    
    Args:
        base_url (str): 服务器地址
        credentials (list): 用户凭据列表 [(username, password), ...]
        delay (int): 请求间隔时间（秒）
    
    Returns:
        list: 测试结果列表
    """
    results = []
    total = len(credentials)
    
    print(f"开始批量测试，共 {total} 个账户")
    print("=" * 60)
    
    for i, (username, password) in enumerate(credentials, 1):
        print(f"[{i}/{total}] 测试账户: {username}")
        
        result = test_login(base_url, username, password)
        results.append(result)
        
        # 输出结果
        status = "✓ 成功" if result['success'] else "✗ 失败"
        print(f"  结果: {status} - {result['message']} ({result['response_time']}ms)")
        
        # 延迟
        if i < total and delay > 0:
            time.sleep(delay)
    
    return results

def main():
    """
    主函数
    """
    print("=" * 60)
    print("奇安信流量传感器批量登录测试工具")
    print("=" * 60)
    
    # 获取配置
    base_url = input("请输入服务器地址 (如 http://*************): ").strip()
    if not base_url:
        print("服务器地址不能为空!")
        return
    
    base_url = base_url.rstrip('/')
    
    # 选择输入方式
    print("\n选择用户凭据输入方式:")
    print("1. 手动输入")
    print("2. 从文件读取")
    choice = input("请选择 (1/2): ").strip()
    
    credentials = []
    
    if choice == '1':
        # 手动输入
        print("\n请输入用户凭据 (输入空行结束):")
        while True:
            username = input("用户名: ").strip()
            if not username:
                break
            password = input("密码: ").strip()
            if password:
                credentials.append((username, password))
    
    elif choice == '2':
        # 从文件读取
        filename = input("请输入文件路径 (支持CSV和TXT): ").strip()
        credentials = load_credentials_from_file(filename)
        
        if not credentials:
            print("未能从文件中读取到有效的用户凭据")
            return
    
    else:
        print("无效选择")
        return
    
    if not credentials:
        print("没有可测试的用户凭据")
        return
    
    # 设置延迟
    delay = input("请输入请求间隔时间(秒，默认1): ").strip()
    try:
        delay = int(delay) if delay else 1
    except ValueError:
        delay = 1
    
    # 执行批量测试
    results = batch_test_login(base_url, credentials, delay)
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    fail_count = len(results) - success_count
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print(f"总计: {len(results)} 个账户")
    print(f"成功: {success_count} 个")
    print(f"失败: {fail_count} 个")
    print("=" * 60)
    
    # 保存结果
    save_choice = input("\n是否保存结果到CSV文件? (y/n): ").strip().lower()
    if save_choice == 'y':
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"login_test_results_{timestamp}.csv"
        save_results_to_csv(results, filename)
    
    # 显示成功的账户
    successful_accounts = [r for r in results if r['success']]
    if successful_accounts:
        print(f"\n成功登录的账户 ({len(successful_accounts)} 个):")
        for account in successful_accounts:
            print(f"  {account['username']}:{account['password']}")

if __name__ == "__main__":
    main()
