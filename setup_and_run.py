#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器登录模拟器 - 安装和运行脚本
自动检查依赖并运行相应的登录工具
"""

import subprocess
import sys
import os

def check_and_install_requirements():
    """
    检查并安装必要的依赖包
    """
    required_packages = [
        'requests>=2.28.0',
        'pycryptodome>=3.15.0',
        'urllib3>=1.26.0'
    ]
    
    print("检查Python依赖包...")
    
    for package in required_packages:
        package_name = package.split('>=')[0]
        try:
            __import__(package_name.replace('-', '_'))
            print(f"✓ {package_name} 已安装")
        except ImportError:
            print(f"✗ {package_name} 未安装，正在安装...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✓ {package_name} 安装成功")
            except subprocess.CalledProcessError:
                print(f"✗ {package_name} 安装失败")
                return False
    
    return True

def show_menu():
    """
    显示菜单选项
    """
    print("\n" + "=" * 60)
    print("奇安信流量传感器登录模拟器")
    print("=" * 60)
    print("请选择要运行的工具:")
    print("1. 完整登录模拟器 (login_simulator.py)")
    print("   - 完整的13步登录流程")
    print("   - 详细的日志输出")
    print("   - 适合深度分析")
    print()
    print("2. 快速登录工具 (quick_login.py)")
    print("   - 简化的登录流程")
    print("   - 快速测试单个账户")
    print("   - 适合日常使用")
    print()
    print("3. 批量登录测试 (batch_test.py)")
    print("   - 批量测试多个账户")
    print("   - 支持文件输入")
    print("   - 生成测试报告")
    print()
    print("4. 查看帮助信息")
    print("5. 退出")
    print("=" * 60)

def run_script(script_name):
    """
    运行指定的脚本
    """
    if not os.path.exists(script_name):
        print(f"错误: 脚本文件 {script_name} 不存在")
        return False
    
    try:
        subprocess.run([sys.executable, script_name], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"运行脚本时发生错误: {e}")
        return False
    except KeyboardInterrupt:
        print("\n用户中断操作")
        return True

def show_help():
    """
    显示帮助信息
    """
    help_text = """
帮助信息
========

1. 完整登录模拟器 (login_simulator.py)
   - 模拟完整的13步登录流程
   - 包括获取配置、验证码、CSRF token等所有步骤
   - 提供详细的执行日志
   - 适合安全研究和深度分析

2. 快速登录工具 (quick_login.py)
   - 只执行核心的登录步骤
   - 更快的执行速度
   - 适合快速验证单个账户

3. 批量登录测试 (batch_test.py)
   - 支持测试多个用户账户
   - 可以从CSV文件读取用户凭据
   - 自动生成测试报告
   - 支持设置请求间隔

文件格式说明:
- CSV文件格式: username,password
- TXT文件格式: username:password 或 username,password

示例CSV文件内容:
admin,123456
root,password
test,test123

注意事项:
- 确保网络连接正常
- 某些功能需要手动输入验证码
- 请遵守相关法律法规，仅用于授权测试
- 建议设置适当的请求间隔，避免被系统限制

常见问题:
Q: 提示缺少依赖包怎么办？
A: 运行 pip install -r requirements.txt

Q: 连接失败怎么办？
A: 检查网络连接和服务器地址是否正确

Q: 验证码识别失败怎么办？
A: 查看生成的captcha.gif文件，手动输入正确的验证码

Q: 登录失败怎么办？
A: 确认用户名密码正确，检查账户是否被锁定
"""
    print(help_text)

def main():
    """
    主函数
    """
    print("正在初始化...")
    
    # 检查并安装依赖
    if not check_and_install_requirements():
        print("依赖包安装失败，请手动安装后重试")
        print("运行命令: pip install -r requirements.txt")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == '1':
                print("\n启动完整登录模拟器...")
                run_script('login_simulator.py')
            
            elif choice == '2':
                print("\n启动快速登录工具...")
                run_script('quick_login.py')
            
            elif choice == '3':
                print("\n启动批量登录测试...")
                run_script('batch_test.py')
            
            elif choice == '4':
                show_help()
                input("\n按回车键继续...")
            
            elif choice == '5':
                print("退出程序")
                break
            
            else:
                print("无效选择，请重新输入")
        
        except KeyboardInterrupt:
            print("\n\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
