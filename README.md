# 奇安信流量传感器登录模拟器

这是一个完整的Python脚本集合，用于模拟奇安信流量传感器的登录过程，包括所有必要的HTTP请求包。

## 脚本说明

### 1. login_simulator.py - 完整登录模拟器
- 完整模拟13个步骤的登录流程
- 自动处理CSRF Token和Session Cookie
- 支持密码加密（AES加密）
- 自动获取和保存验证码图片
- 详细的日志输出，便于调试
- 模拟真实浏览器的请求头和行为

### 2. quick_login.py - 快速登录工具
- 简化版本，只包含核心登录步骤
- 适合快速测试单个账户
- 更少的网络请求，更快的执行速度

### 3. batch_test.py - 批量登录测试工具
- 支持批量测试多个用户账户
- 可从CSV文件或手动输入用户凭据
- 自动生成测试报告
- 支持设置请求间隔，避免被限制

## 功能特性

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests pycryptodome urllib3
```

## 使用方法

### 1. 完整登录模拟器 (login_simulator.py)

```bash
python login_simulator.py
```

运行后会提示输入：
1. 用户名（默认为admin）
2. 密码
3. 验证码（会自动下载验证码图片captcha.gif）

### 2. 快速登录工具 (quick_login.py)

```bash
python quick_login.py
```

适合快速测试单个账户，只执行核心登录步骤。

### 3. 批量登录测试 (batch_test.py)

```bash
python batch_test.py
```

支持两种输入方式：
- 手动输入多个用户凭据
- 从CSV文件读取用户凭据（参考 credentials_example.csv）

### 代码中使用

```python
from login_simulator import QianXinLoginSimulator

# 创建模拟器实例
simulator = QianXinLoginSimulator("http://*************")

# 执行完整登录流程
success = simulator.simulate_complete_login("admin", "your_password", "captcha_code")

if success:
    print("登录成功!")
else:
    print("登录失败!")
```

### 快速登录示例

```python
from quick_login import quick_login

# 快速登录测试
success = quick_login("http://*************", "admin", "password")
```

## 登录流程说明

脚本模拟了以下13个步骤：

1. **获取登录页面** - 获取初始session和CSRF token
2. **获取项目配置** - 加载项目配置文件
3. **获取网站图标** - 加载favicon.ico
4. **检查登录状态** - 验证当前登录状态
5. **获取站点地图** - 加载sitemap.json
6. **检查更新状态** - 检查系统是否在更新
7. **切换语言设置** - 设置界面语言
8. **获取登录微应用** - 加载登录相关的前端应用
9. **获取系统Logo** - 加载系统标识图片
10. **获取许可证信息** - 获取系统许可证状态
11. **检查代理状态** - 检查是否启用代理
12. **获取验证码** - 下载验证码图片
13. **执行登录** - 提交登录凭据

## 配置说明

### 修改目标服务器

在 `main()` 函数中修改 `BASE_URL` 变量：

```python
BASE_URL = "http://your-server-ip"  # 修改为实际的服务器地址
```

### 自定义请求头

可以在 `QianXinLoginSimulator.__init__()` 方法中修改请求头：

```python
self.session.headers.update({
    'User-Agent': 'Your Custom User Agent',
    # 其他自定义头部
})
```

## 密码加密

脚本实现了与前端相同的AES密码加密逻辑：
- 使用MD5生成密钥
- AES-CBC模式加密
- Base64编码输出
- 兼容CryptoJS格式

## 验证码处理

- 自动下载验证码图片保存为 `captcha.gif`
- 支持手动输入验证码
- 可以通过参数传入验证码避免交互

## 错误处理

- 每个步骤都有独立的错误处理
- 即使某个步骤失败，也会继续执行后续步骤
- 详细的日志输出便于问题定位

## 注意事项

1. **网络连接**: 确保能够访问目标服务器
2. **证书验证**: 如果是HTTPS且证书有问题，可能需要禁用SSL验证
3. **防火墙**: 确保没有防火墙阻止连接
4. **验证码**: 需要人工识别验证码图片
5. **合法使用**: 仅用于授权的安全测试，请遵守相关法律法规

## 故障排除

### 连接失败
- 检查网络连接
- 确认服务器地址正确
- 检查防火墙设置

### 登录失败
- 确认用户名密码正确
- 检查验证码输入是否正确
- 查看详细错误信息

### CSRF Token错误
- 重新运行脚本获取新的token
- 检查session cookie是否正确

## 开发说明

### 添加新步骤

1. 在类中添加新的步骤方法：
```python
def step_new_action(self):
    print("执行新动作...")
    # 实现逻辑
    return True
```

2. 在 `simulate_complete_login` 方法的steps列表中添加新步骤

### 修改加密算法

如果目标系统的加密算法有变化，可以修改 `encrypt_password` 方法。

## 许可证

本项目仅用于教育和授权的安全测试目的。使用者需要确保遵守相关法律法规。

## 版本历史

- v1.0.0: 初始版本，支持完整的13步登录流程
