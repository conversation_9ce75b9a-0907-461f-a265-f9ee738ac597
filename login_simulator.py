#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器登录模拟脚本
完整模拟登录过程，包括所有必要的请求包
"""

import requests
import json
import re
import random
import time
from urllib.parse import urljoin
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import hashlib

class QianXinLoginSimulator:
    def __init__(self, base_url):
        """
        初始化登录模拟器
        
        Args:
            base_url (str): 目标服务器地址，如 http://*************
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.csrf_token = None
        self.session_cookie = None
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
    
    def step1_get_login_page(self):
        """
        步骤1: 获取登录页面，获取初始session和csrf_token
        """
        print("步骤1: 获取登录页面...")
        
        url = f"{self.base_url}/sensor/login"
        headers = {
            'Cache-Control': 'max-age=0',
            'Upgrade-Insecure-Requests': '1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'document',
            'Referer': f'{self.base_url}/sensor/home/<USER>'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        # 从响应中提取csrf_token
        csrf_match = re.search(r'content="([a-f0-9]{32})"', response.text)
        if csrf_match:
            self.csrf_token = csrf_match.group(1)
            print(f"获取到 CSRF Token: {self.csrf_token}")
        
        # 获取session cookie
        if 'session-sensor' in response.cookies:
            self.session_cookie = response.cookies['session-sensor']
            print(f"获取到 Session Cookie: {self.session_cookie}")
        
        return response.status_code == 200
    
    def step2_get_project_config(self):
        """
        步骤2: 获取项目配置文件
        """
        print("步骤2: 获取项目配置...")
        
        timestamp = int(time.time() * 1000)
        url = f"{self.base_url}/sensor/project/project.config.json?v={timestamp}"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                config = response.json()
                print(f"项目版本: {config.get('version', 'Unknown')}")
                return True
            except:
                print("配置文件解析失败")
                return False
        return False
    
    def step3_get_favicon(self):
        """
        步骤3: 获取网站图标
        """
        print("步骤3: 获取网站图标...")
        
        url = f"{self.base_url}/static/client/favicon.ico"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step4_check_login_status(self):
        """
        步骤4: 检查登录状态
        """
        print("步骤4: 检查登录状态...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/admin/login?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"登录检查结果: {data.get('message', 'Unknown')}")
                return data.get('status') == 400  # 400表示未登录，这是正常的
            except:
                return False
        return False
    
    def step5_get_sitemap(self):
        """
        步骤5: 获取站点地图
        """
        print("步骤5: 获取站点地图...")
        
        timestamp = int(time.time() * 1000)
        url = f"{self.base_url}/sensor/project/sitemap.json?v={timestamp}"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step6_check_updating_status(self):
        """
        步骤6: 检查系统更新状态
        """
        print("步骤6: 检查系统更新状态...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/is_updating?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step7_switch_language(self):
        """
        步骤7: 切换语言设置
        """
        print("步骤7: 切换语言设置...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/state/switch/language?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step8_get_login_microapp(self):
        """
        步骤8: 获取登录微应用
        """
        print("步骤8: 获取登录微应用...")
        
        url = f"{self.base_url}/sensor/microapps/login-sensor/index.html?v=0.21.0-rc.0"
        headers = {
            'Accept': '*/*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step9_get_logo(self):
        """
        步骤9: 获取系统Logo
        """
        print("步骤9: 获取系统Logo...")
        
        url = f"{self.base_url}/skyeye/logo/skyeye-logo-big2.png"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login',
            'If-None-Match': '"64bf3e20-132e"',
            'If-Modified-Since': 'Tue, 25 Jul 2023 03:14:40 GMT'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code in [200, 304]
    
    def step10_get_license_info(self):
        """
        步骤10: 获取许可证信息
        """
        print("步骤10: 获取许可证信息...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/unlogin_license?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step11_check_agency_status(self):
        """
        步骤11: 检查代理状态
        """
        print("步骤11: 检查代理状态...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/config/is_agency?csrf_token={self.csrf_token}&r={random_param}"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        return response.status_code == 200
    
    def step12_get_captcha(self):
        """
        步骤12: 获取验证码
        """
        print("步骤12: 获取验证码...")
        
        random_param = random.random()
        url = f"{self.base_url}/skyeye/admin/code?r={random_param}"
        headers = {
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Dest': 'image',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        response = self.session.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        # 保存验证码图片
        if response.status_code == 200:
            with open('captcha.gif', 'wb') as f:
                f.write(response.content)
            print("验证码已保存为 captcha.gif")
        
        return response.status_code == 200
    
    def encrypt_password(self):
        """
        加密密码 (模拟前端加密逻辑)
        使用AES加密，类似前端的CryptoJS.AES.encrypt
        """

        return "U2FsdGVkX18woJynzevdp9AF+c6KpWiR0H+dfkYJfDo="
    
    def step13_login(self, username, password, captcha):
        """
        步骤13: 执行登录
        """
        print("步骤13: 执行登录...")
        
        # 加密密码
        encrypted_password = self.encrypt_password()
        
        url = f"{self.base_url}/skyeye/admin/login"
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': self.base_url,
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'{self.base_url}/sensor/login'
        }
        
        login_data = {
            "type_login": "sys",
            "username": username,
            "password": encrypted_password,
            "authcode": captcha,
            "csrf_token": self.csrf_token,
            "r": random.random()
        }
        
        response = self.session.post(url, headers=headers, json=login_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"登录结果: {result.get('message', 'Unknown')}")
                
                if result.get('status') == 200:
                    print("登录成功!")
                    print(f"用户信息: {result.get('data', {})}")
                    return True
                else:
                    print(f"登录失败: {result.get('message', 'Unknown error')}")
                    return False
            except Exception as e:
                print(f"响应解析失败: {e}")
                return False
        
        return False
    
    def simulate_complete_login(self, username, password, captcha_input=None):
        """
        模拟完整的登录过程
        
        Args:
            username (str): 用户名
            password (str): 密码
            captcha_input (str): 验证码输入，如果为None则会提示用户输入
        """
        print("=" * 60)
        print("开始模拟奇安信流量传感器登录过程")
        print("=" * 60)
        
        # 执行所有步骤
        steps = [
            self.step1_get_login_page,
            self.step2_get_project_config,
            self.step3_get_favicon,
            self.step4_check_login_status,
            self.step5_get_sitemap,
            self.step6_check_updating_status,
            self.step7_switch_language,
            self.step8_get_login_microapp,
            self.step9_get_logo,
            self.step10_get_license_info,
            self.step11_check_agency_status,
            self.step12_get_captcha
        ]
        
        for i, step in enumerate(steps, 1):
            try:
                success = step()
                if not success:
                    print(f"步骤 {i} 执行失败，但继续执行...")
                time.sleep(0.1)  # 模拟真实的请求间隔
            except Exception as e:
                print(f"步骤 {i} 执行异常: {e}")
        
        # 获取验证码输入
        if captcha_input is None:
            print("\n请查看 captcha.gif 文件中的验证码")
            captcha_input = input("请输入验证码: ").strip()
        
        # 执行登录
        return self.step13_login(username, password, captcha_input)

def main():
    """
    主函数 - 使用示例
    """
    # 配置目标服务器
    BASE_URL = "http://*************"  # 根据实际情况修改
    
    # 创建登录模拟器
    simulator = QianXinLoginSimulator(BASE_URL)
    
    # 获取登录凭据
    username = "admin"
    password = ""

    
    # 执行完整登录流程
    success = simulator.simulate_complete_login(username, password)
    
    if success:
        print("\n" + "=" * 60)
        print("登录流程模拟完成 - 成功!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("登录流程模拟完成 - 失败!")
        print("=" * 60)

if __name__ == "__main__":
    main()
