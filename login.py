import requests
import ddddocr
import json
import time  # 导入 time 模块，用于生成唯一的文件名
import random # 导入 random 模块，用于生成随机数 r
import secrets # 导入 secrets 模块，用于生成安全的随机 token

# --- 配置信息 ---
# 目标系统的基础 URL 地址。
# 重要提示：原始请求使用的是 IP 地址。如果您的环境地址不同，请替换成正确的地址。
BASE_URL = "https://*************"

# --- 创建一个 requests 会话 (session) ---
# 使用 session 对象可以在多次请求之间保持 cookie，这对于需要登录的场景至关重要。
session = requests.Session()

# 原始请求可能因为服务器使用自签名 SSL 证书而导致验证失败。
# 将 verify 设置为 False 可以禁用 SSL 证书验证。
# 请注意：在生产环境或公共网络中，这可能存在安全风险。
session.verify = False
# 为了避免控制台输出烦人的 InsecureRequestWarning 警告信息，我们将其禁用。
from urllib3.exceptions import InsecureRequestWarning
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


# --- 定义请求头 (Headers) ---
# 这些请求头信息是从您提供的原始数据包中复制的，目的是为了模拟一个真实的浏览器行为。
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Origin": BASE_URL,
    "Referer": f"{BASE_URL}/sensor/login",
    "sec-ch-ua": '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "Content-Type": "application/json",
}

def get_verification_code():
    """
    获取验证码图片，先保存到本地，然后识别其中的文字。
    """
    try:
        # --- 新增代码：为验证码请求生成一个随机数 r ---
        random_r = random.random()
        
        # 步骤 1: 请求验证码图片
        # --- 修改代码：在 URL 中添加 r 参数 ---
        code_url = f"{BASE_URL}/skyeye/admin/code?r={random_r}"
        print(f"正在从以下地址获取验证码: {code_url}")
        
        # 请求图片时，Accept 类型需要设置为图片类型。
        response_image = session.get(code_url, headers={**headers, "Accept": "image/gif"})
        response_image.raise_for_status()  # 如果请求失败 (例如状态码为 4xx 或 5xx), 则会抛出异常。

        image_content = response_image.content

        # --- 新增代码：将验证码图片保存到本地 ---
        # 为了防止重名，可以使用时间戳为图片命名。
        # 服务器返回的 Content-Type 是 image/gif，所以我们以 .gif 后缀保存。
        image_filename = f"verification_code_{int(time.time())}.gif"
        with open(image_filename, 'wb') as f:
            f.write(image_content)
        print(f"验证码图片已成功保存为: {image_filename}")
        # --- 新增代码结束 ---

        # 步骤 2: 使用 ddddocr 从图片内容的二进制数据中识别验证码
        # 注意：这里仍然使用内存中的 image_content 进行识别，效率更高。
        ocr = ddddocr.DdddOcr()
        result = ocr.classification(image_content)
        print(f"成功识别验证码: {result}")
        return result

    except requests.exceptions.RequestException as e:
        print(f"获取图片时发生网络错误: {e}")
        return None
    except Exception as e:
        print(f"处理图片或OCR识别过程中发生错误: {e}")
        return None

def login(authcode):
    """
    使用获取到的验证码尝试登录。
    """
    if not authcode:
        print("因为缺少验证码，登录已中止。")
        return

    try:
        # 步骤 3: 构造并发送登录的 POST 请求
        login_url = f"{BASE_URL}/skyeye/admin/login"

        # --- 修改代码：动态生成 csrf_token 和 r ---
        # 生成一个新的 32 位十六进制字符串作为 CSRF 令牌。
        dynamic_csrf_token = secrets.token_hex(16)
        # 为登录请求生成一个新的随机数 r。
        random_r_login = random.random()
        
        # 登录的载荷 (payload) 来自您提供的示例数据包。
        # --- 修改代码：使用动态生成的 csrf_token 和 r ---
        login_payload = {
            "type_login": "sys",
            "username": "admin",
            "password": "U2FsdGVkX1+YXgeiL50obVyy8VSRSO8DJo80X6fFmac=",
            "authcode": authcode,
            "csrf_token": dynamic_csrf_token, # 使用动态生成的 token
            "r": random_r_login # 使用动态生成的随机数
        }

        print(f"正在向以下地址发送登录请求: {login_url}")
        print(f"发送的载荷 (Payload): \n{json.dumps(login_payload, indent=2, ensure_ascii=False)}")

        response_login = session.post(login_url, headers=headers, json=login_payload)
        response_login.raise_for_status()

        # 步骤 4: 打印服务器的响应结果
        print("\n--- 登录响应 ---")
        print(f"状态码: {response_login.status_code}")
        # 尝试以 JSON 格式打印响应体，如果失败则以纯文本格式打印。
        try:
            print("响应 JSON 内容:")
            # 使用 indent=2 美化输出, ensure_ascii=False 保证中文正常显示
            print(json.dumps(response_login.json(), indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print("响应文本内容:")
            print(response_login.text)

    except requests.exceptions.RequestException as e:
        print(f"登录请求过程中发生网络错误: {e}")

# 当脚本作为主程序运行时，执行以下代码
if __name__ == "__main__":
    verification_code = get_verification_code()
    # 只有成功获取到验证码后才尝试登录
    if verification_code:
        login(verification_code)