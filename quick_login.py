#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奇安信流量传感器快速登录脚本
简化版本，只包含核心登录步骤
"""

import requests
import json
import re
import random
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import hashlib

def encrypt_password(password, key="skyeye"):
    """
    加密密码 (模拟前端加密逻辑)
    """
    try:
        # 生成密钥
        key_bytes = key.encode('utf-8')
        key_hash = hashlib.md5(key_bytes).digest()
        
        # 生成随机盐值
        salt = b'Salted__' + bytes([random.randint(0, 255) for _ in range(8)])
        
        # 使用PBKDF1类似的密钥派生
        derived_key = hashlib.md5(key_hash + salt[8:]).digest()
        
        # AES加密
        cipher = AES.new(derived_key, AES.MODE_CBC, derived_key[:16])
        padded_password = pad(password.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_password)
        
        # 组合盐值和加密数据
        encrypted_data = salt + encrypted
        
        # Base64编码
        return base64.b64encode(encrypted_data).decode('utf-8')
    except Exception as e:
        print(f"密码加密失败: {e}")
        # 如果加密失败，返回示例中的加密密码
        return "U2FsdGVkX18woJynzevdp9AF+c6KpWiR0H+dfkYJfDo="

def quick_login(base_url, username, password, captcha=None):
    """
    快速登录函数
    
    Args:
        base_url (str): 服务器地址
        username (str): 用户名
        password (str): 密码
        captcha (str): 验证码，如果为None则会自动获取并提示输入
    
    Returns:
        bool: 登录是否成功
    """
    session = requests.Session()
    
    # 设置请求头
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    })
    
    try:
        # 步骤1: 获取登录页面和CSRF token
        print("1. 获取登录页面...")
        login_page_url = f"{base_url}/sensor/login"
        response = session.get(login_page_url)
        
        if response.status_code != 200:
            print(f"获取登录页面失败: {response.status_code}")
            return False
        
        # 提取CSRF token
        csrf_match = re.search(r'content="([a-f0-9]{32})"', response.text)
        if not csrf_match:
            print("未找到CSRF token")
            return False
        
        csrf_token = csrf_match.group(1)
        print(f"获取到CSRF token: {csrf_token}")
        
        # 步骤2: 获取验证码（如果需要）
        if captcha is None:
            print("2. 获取验证码...")
            captcha_url = f"{base_url}/skyeye/admin/code?r={random.random()}"
            captcha_response = session.get(captcha_url)
            
            if captcha_response.status_code == 200:
                with open('captcha.gif', 'wb') as f:
                    f.write(captcha_response.content)
                print("验证码已保存为 captcha.gif")
                captcha = input("请输入验证码: ").strip()
            else:
                print("获取验证码失败")
                return False
        
        # 步骤3: 执行登录
        print("3. 执行登录...")
        
        # 加密密码
        encrypted_password = encrypt_password(password)
        
        login_url = f"{base_url}/skyeye/admin/login"
        login_data = {
            "type_login": "sys",
            "username": username,
            "password": encrypted_password,
            "authcode": captcha,
            "csrf_token": csrf_token,
            "r": random.random()
        }
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Origin': base_url,
            'Referer': f'{base_url}/sensor/login'
        }
        
        login_response = session.post(login_url, headers=headers, json=login_data)
        
        if login_response.status_code == 200:
            result = login_response.json()
            print(f"登录结果: {result.get('message', 'Unknown')}")
            
            if result.get('status') == 200:
                print("登录成功!")
                user_data = result.get('data', {})
                print(f"用户: {user_data.get('username', 'Unknown')}")
                print(f"管理员: {'是' if user_data.get('admin') else '否'}")
                return True
            else:
                print(f"登录失败: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"登录请求失败: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"登录过程中发生错误: {e}")
        return False

def main():
    """
    主函数
    """
    print("=" * 50)
    print("奇安信流量传感器快速登录工具")
    print("=" * 50)
    
    # 获取配置
    base_url = input("请输入服务器地址 (如 http://*************): ").strip()
    if not base_url:
        print("服务器地址不能为空!")
        return
    
    # 移除末尾的斜杠
    base_url = base_url.rstrip('/')
    
    username = input("请输入用户名 (默认: admin): ").strip() or "admin"
    password = input("请输入密码: ").strip()
    
    if not password:
        print("密码不能为空!")
        return
    
    # 执行登录
    success = quick_login(base_url, username, password)
    
    if success:
        print("\n" + "=" * 50)
        print("登录成功!")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("登录失败!")
        print("=" * 50)

if __name__ == "__main__":
    main()
